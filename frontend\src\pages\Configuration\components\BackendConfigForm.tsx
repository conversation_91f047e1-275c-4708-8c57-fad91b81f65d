/**
 * 后端配置表单组件
 * 用于创建和编辑后端配置
 */

import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Space,
  Card,
  Row,
  Col,
  message,
  Spin,
  Alert,
  Tooltip
} from 'antd';
import {
  SaveOutlined,
  CloseOutlined,
  ThunderboltOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

import { 
  backendConfigApi,
  BackendConfigData,
  BackendConfigCreate,
  BackendConfigUpdate
} from '../../../services';

const { Option } = Select;
const { TextArea } = Input;

interface BackendConfigFormProps {
  configId?: string | null;
  onSuccess: () => void;
  onCancel: () => void;
}

const BackendConfigForm: React.FC<BackendConfigFormProps> = ({
  configId,
  onSuccess,
  onCancel
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [testing, setTesting] = useState(false);
  const [, setConfigData] = useState<BackendConfigData | null>(null);

  // 如果是编辑模式，加载配置数据
  useEffect(() => {
    if (configId) {
      loadConfigData();
    } else {
      // 新建模式，设置默认值
      setDefaultValues();
    }
  }, [configId]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadConfigData = async () => {
    setLoading(true);
    try {
      const data = await backendConfigApi.getConfig(configId!);
      setConfigData(data);
      
      // 设置表单值
      form.setFieldsValue({
        backend_name: data.backend_name,
        description: data.description,
        api_endpoint: data.api_endpoint,
        timeout: data.timeout,
        max_retries: data.max_retries,
        max_concurrent: data.max_concurrent,
        mean_delay: data.mean_delay,
        max_range: data.max_range,
        pool_size: data.pool_size,
        memory_threshold: data.memory_threshold,
        weight: data.weight,
        priority: data.priority,
        performance_level: data.performance_level,
        health_check_interval: data.health_check_interval,
        health_check_timeout: data.health_check_timeout,
        failure_threshold: data.failure_threshold,
        recovery_threshold: data.recovery_threshold,
        auth_type: data.auth_config.auth_type,
        api_key: data.auth_config.api_key,
        username: data.auth_config.username,
        password: data.auth_config.password,
        token: data.auth_config.token,
      });
    } catch (error) {
      message.error(`加载配置数据失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const setDefaultValues = () => {
    form.setFieldsValue({
      timeout: 30000,
      max_retries: 3,
      max_concurrent: 2,
      mean_delay: 1000,
      max_range: 2000,
      pool_size: 10,
      memory_threshold: 512,  // 修复：至少128MB，设置为512MB
      weight: 1,
      priority: 1,
      performance_level: 'medium',
      health_check_interval: 60,  // 修复：至少10秒，设置为60秒
      health_check_timeout: 5000,  // 修复：至少1000ms，设置为5000ms
      failure_threshold: 3,
      recovery_threshold: 2,
      auth_type: 'none',
    });
  };

  const handleTestConnection = async () => {
    try {
      const values = await form.validateFields([
        'backend_name',
        'api_endpoint',
        'timeout',
        'max_retries',
        'auth_type',
        'api_key',
        'username',
        'password',
        'token'
      ]);

      setTesting(true);
      
      const testData: BackendConfigCreate = {
        backend_name: values.backend_name,
        api_endpoint: values.api_endpoint,
        timeout: values.timeout,
        max_retries: values.max_retries,
        auth_config: {
          auth_type: values.auth_type,
          api_key: values.api_key,
          username: values.username,
          password: values.password,
          token: values.token,
        },
      };

      const result = await backendConfigApi.testConnection(testData);
      
      if (result.success) {
        message.success(`连接测试成功！响应时间: ${result.response_time}ms`);
        if (result.api_version) {
          message.info(`API版本: ${result.api_version}`);
        }
      } else {
        message.error(`连接测试失败: ${result.error_message}`);
      }
    } catch (error: any) {
      if (error.errorFields) {
        message.warning('请先填写必要的连接信息');
      } else {
        message.error(`连接测试失败: ${error}`);
      }
    } finally {
      setTesting(false);
    }
  };

  const handleSubmit = async (values: any) => {
    setSubmitting(true);
    try {
      const configData: BackendConfigCreate | BackendConfigUpdate = {
        backend_name: values.backend_name,
        description: values.description,
        api_endpoint: values.api_endpoint,
        timeout: values.timeout,
        max_retries: values.max_retries,
        max_concurrent: values.max_concurrent,
        mean_delay: values.mean_delay,
        max_range: values.max_range,
        pool_size: values.pool_size,
        memory_threshold: values.memory_threshold,
        weight: values.weight,
        priority: values.priority,
        performance_level: values.performance_level,
        health_check_interval: values.health_check_interval,
        health_check_timeout: values.health_check_timeout,
        failure_threshold: values.failure_threshold,
        recovery_threshold: values.recovery_threshold,
        auth_config: {
          auth_type: values.auth_type,
          api_key: values.api_key,
          username: values.username,
          password: values.password,
          token: values.token,
        },
      };

      if (configId) {
        // 更新配置
        await backendConfigApi.updateConfig(configId, configData as BackendConfigUpdate);
        message.success('更新后端配置成功');
      } else {
        // 创建配置
        await backendConfigApi.createConfig(configData as BackendConfigCreate);
        message.success('创建后端配置成功');
      }
      
      onSuccess();
    } catch (error) {
      message.error(`${configId ? '更新' : '创建'}后端配置失败: ${error}`);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      scrollToFirstError
    >
      {/* 基本信息 */}
      <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="backend_name"
              label="配置名称"
              rules={[{ required: true, message: '请输入配置名称' }]}
            >
              <Input placeholder="请输入配置名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="performance_level"
              label="性能等级"
              rules={[{ required: true, message: '请选择性能等级' }]}
            >
              <Select>
                <Option value="low">低</Option>
                <Option value="medium">中</Option>
                <Option value="high">高</Option>
                <Option value="ultra">超高</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Form.Item
          name="description"
          label="描述"
        >
          <TextArea rows={2} placeholder="请输入配置描述" />
        </Form.Item>
      </Card>

      {/* 连接配置 */}
      <Card 
        title="连接配置" 
        size="small" 
        style={{ marginBottom: 16 }}
        extra={
          <Button
            type="link"
            icon={<ThunderboltOutlined />}
            loading={testing}
            onClick={handleTestConnection}
          >
            测试连接
          </Button>
        }
      >
        <Alert
          message="连接配置"
          description="配置后端API的连接参数，建议先测试连接确保配置正确"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        <Form.Item
          name="api_endpoint"
          label={
            <span>
              API端点
              <Tooltip title="Docker环境中请使用: http://localhost:11235 (系统会自动转换为host.docker.internal)">
                <InfoCircleOutlined style={{ marginLeft: 4 }} />
              </Tooltip>
            </span>
          }
          rules={[
            { required: true, message: '请输入API端点' },
            { type: 'url', message: '请输入有效的URL' }
          ]}
        >
          <Input placeholder="http://localhost:11235" />
        </Form.Item>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="timeout"
              label={
                <span>
                  超时时间(毫秒)
                  <Tooltip title="请求超时时间，建议设置为30000毫秒">
                    <InfoCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              }
              rules={[{ required: true, message: '请输入超时时间' }]}
            >
              <InputNumber min={1000} max={300000} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="max_retries"
              label="最大重试次数"
              rules={[{ required: true, message: '请输入最大重试次数' }]}
            >
              <InputNumber min={0} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      {/* 认证配置 */}
      <Card title="认证配置" size="small" style={{ marginBottom: 16 }}>
        <Form.Item
          name="auth_type"
          label="认证类型"
          rules={[{ required: true, message: '请选择认证类型' }]}
        >
          <Select onChange={(value) => {
            // 清空其他认证字段
            if (value === 'none') {
              form.setFieldsValue({ api_key: '', username: '', password: '', token: '' });
            }
          }}>
            <Option value="none">无认证</Option>
            <Option value="api_key">API密钥</Option>
            <Option value="basic">基础认证</Option>
            <Option value="bearer">Bearer令牌</Option>
          </Select>
        </Form.Item>

        <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => 
          prevValues.auth_type !== currentValues.auth_type
        }>
          {({ getFieldValue }) => {
            const authType = getFieldValue('auth_type');
            
            if (authType === 'api_key') {
              return (
                <Form.Item
                  name="api_key"
                  label="API密钥"
                  rules={[{ required: true, message: '请输入API密钥' }]}
                >
                  <Input.Password placeholder="请输入API密钥" />
                </Form.Item>
              );
            }
            
            if (authType === 'basic') {
              return (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="username"
                      label="用户名"
                      rules={[{ required: true, message: '请输入用户名' }]}
                    >
                      <Input placeholder="请输入用户名" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="password"
                      label="密码"
                      rules={[{ required: true, message: '请输入密码' }]}
                    >
                      <Input.Password placeholder="请输入密码" />
                    </Form.Item>
                  </Col>
                </Row>
              );
            }
            
            if (authType === 'bearer') {
              return (
                <Form.Item
                  name="token"
                  label="Bearer令牌"
                  rules={[{ required: true, message: '请输入Bearer令牌' }]}
                >
                  <Input.Password placeholder="请输入Bearer令牌" />
                </Form.Item>
              );
            }
            
            return null;
          }}
        </Form.Item>
      </Card>

      {/* 性能配置 */}
      <Card title="性能配置" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="max_concurrent"
              label="最大并发数"
              rules={[{ required: true, message: '请输入最大并发数' }]}
            >
              <InputNumber min={1} max={100} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="weight"
              label="权重"
              rules={[{ required: true, message: '请输入权重' }]}
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="priority"
              label="优先级"
              rules={[{ required: true, message: '请输入优先级' }]}
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="mean_delay"
              label="平均延迟(毫秒)"
              rules={[{ required: true, message: '请输入平均延迟' }]}
            >
              <InputNumber min={0} max={10000} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="max_range"
              label="最大范围(毫秒)"
              rules={[{ required: true, message: '请输入最大范围' }]}
            >
              <InputNumber min={0} max={20000} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="pool_size"
              label="连接池大小"
              rules={[{ required: true, message: '请输入连接池大小' }]}
            >
              <InputNumber min={1} max={100} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item
          name="memory_threshold"
          label="内存阈值(MB)"
          rules={[{ required: true, message: '请输入内存阈值' }]}
        >
          <InputNumber min={128} max={4096} style={{ width: '100%' }} />
        </Form.Item>
      </Card>

      {/* 健康检查配置 */}
      <Card title="健康检查配置" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="health_check_interval"
              label="检查间隔(秒)"
              rules={[{ required: true, message: '请输入检查间隔' }]}
            >
              <InputNumber min={30} max={3600} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="health_check_timeout"
              label="检查超时(毫秒)"
              rules={[{ required: true, message: '请输入检查超时' }]}
            >
              <InputNumber min={1000} max={30000} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="failure_threshold"
              label="失败阈值"
              rules={[{ required: true, message: '请输入失败阈值' }]}
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="recovery_threshold"
              label="恢复阈值"
              rules={[{ required: true, message: '请输入恢复阈值' }]}
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      {/* 操作按钮 */}
      <div style={{ textAlign: 'right', marginTop: 24 }}>
        <Space>
          <Button onClick={onCancel}>
            <CloseOutlined /> 取消
          </Button>
          <Button 
            type="primary" 
            htmlType="submit" 
            loading={submitting}
          >
            <SaveOutlined /> {configId ? '更新' : '创建'}
          </Button>
        </Space>
      </div>
    </Form>
  );
};

export default BackendConfigForm;
