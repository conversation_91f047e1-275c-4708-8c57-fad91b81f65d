/**
 * 实时更新Hook
 * 提供任务执行状态、Worker分配状态、URL处理进度的实时更新功能
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { websocketService, EventType, WebSocketEvent } from '../services/websocketService';

export interface TaskProgress {
  taskId: string;
  totalUrls: number;
  completedUrls: number;
  failedUrls: number;
  processingUrls: number;
  progress: number;
  estimatedTimeRemaining?: number;
  currentPhase?: string;
}

export interface WorkerStatus {
  workerId: string;
  status: 'online' | 'offline' | 'busy' | 'idle';
  currentLoad: number;
  maxConcurrent: number;
  assignedUrls: string[];
  performance: {
    successRate: number;
    avgResponseTime: number;
    tasksCompleted: number;
  };
}

export interface UrlProcessingStatus {
  urlId: string;
  url: string;
  status: 'pending' | 'assigned' | 'processing' | 'completed' | 'failed';
  workerId?: string;
  startTime?: string;
  endTime?: string;
  processingTime?: number;
  result?: any;
  error?: string;
}

export interface RealTimeState {
  taskProgress: Record<string, TaskProgress>;
  workerStatus: Record<string, WorkerStatus>;
  urlStatus: Record<string, UrlProcessingStatus>;
  connectionStatus: 'connected' | 'connecting' | 'disconnected' | 'error';
  lastUpdate: string;
}

export interface UseRealTimeUpdatesOptions {
  taskId?: string;
  workerId?: string;
  autoConnect?: boolean;
  onTaskProgress?: (progress: TaskProgress) => void;
  onWorkerStatusChange?: (status: WorkerStatus) => void;
  onUrlStatusChange?: (status: UrlProcessingStatus) => void;
  onConnectionChange?: (status: string) => void;
}

export function useRealTimeUpdates(options: UseRealTimeUpdatesOptions = {}) {
  const {
    taskId,
    workerId,
    autoConnect = true,
    onTaskProgress,
    onWorkerStatusChange,
    onUrlStatusChange,
    onConnectionChange
  } = options;

  const [state, setState] = useState<RealTimeState>({
    taskProgress: {},
    workerStatus: {},
    urlStatus: {},
    connectionStatus: 'disconnected',
    lastUpdate: new Date().toISOString()
  });

  const unsubscribeRefs = useRef<(() => void)[]>([]);

  // 更新状态的辅助函数
  const updateState = useCallback((updater: (prevState: RealTimeState) => RealTimeState) => {
    setState(prevState => {
      const newState = updater(prevState);
      return {
        ...newState,
        lastUpdate: new Date().toISOString()
      };
    });
  }, []);

  // 处理任务进度事件
  const handleTaskProgress = useCallback((event: WebSocketEvent) => {
    const progressData = event.data as TaskProgress;
    
    updateState(prevState => ({
      ...prevState,
      taskProgress: {
        ...prevState.taskProgress,
        [progressData.taskId]: progressData
      }
    }));

    onTaskProgress?.(progressData);
  }, [updateState, onTaskProgress]);

  // 处理Worker状态事件
  const handleWorkerStatus = useCallback((event: WebSocketEvent) => {
    const workerData = event.data as WorkerStatus;
    
    updateState(prevState => ({
      ...prevState,
      workerStatus: {
        ...prevState.workerStatus,
        [workerData.workerId]: workerData
      }
    }));

    onWorkerStatusChange?.(workerData);
  }, [updateState, onWorkerStatusChange]);

  // 处理URL状态事件
  const handleUrlStatus = useCallback((event: WebSocketEvent) => {
    const urlData = event.data as UrlProcessingStatus;
    
    updateState(prevState => ({
      ...prevState,
      urlStatus: {
        ...prevState.urlStatus,
        [urlData.urlId]: urlData
      }
    }));

    onUrlStatusChange?.(urlData);
  }, [updateState, onUrlStatusChange]);

  // 处理Worker分配事件
  const handleWorkerAllocation = useCallback((event: WebSocketEvent) => {
    const allocationData = event.data;
    
    // 更新Worker状态
    if (allocationData.workers) {
      updateState(prevState => {
        const newWorkerStatus = { ...prevState.workerStatus };
        
        allocationData.workers.forEach((worker: any) => {
          newWorkerStatus[worker.workerId] = {
            ...newWorkerStatus[worker.workerId],
            assignedUrls: worker.assignedUrls || [],
            currentLoad: worker.currentLoad || 0
          };
        });
        
        return {
          ...prevState,
          workerStatus: newWorkerStatus
        };
      });
    }
  }, [updateState]);

  // 连接状态监控
  useEffect(() => {
    const checkConnection = () => {
      const status = websocketService.connectionState as any;
      setState(prevState => ({
        ...prevState,
        connectionStatus: status
      }));
      onConnectionChange?.(status);
    };

    // 初始检查
    checkConnection();

    // 定期检查连接状态
    const interval = setInterval(checkConnection, 1000);

    return () => clearInterval(interval);
  }, [onConnectionChange]);

  // 设置事件监听器
  useEffect(() => {
    if (!autoConnect) return;

    const setupSubscriptions = async () => {
      try {
        // 确保WebSocket连接
        if (!websocketService.isConnected) {
          await websocketService.connect();
        }

        // 清理之前的订阅
        unsubscribeRefs.current.forEach(unsubscribe => unsubscribe());
        unsubscribeRefs.current = [];

        // 订阅任务进度事件
        unsubscribeRefs.current.push(
          websocketService.subscribe(EventType.TASK_PROGRESS, handleTaskProgress),
          websocketService.subscribe(EventType.TASK_STARTED, handleTaskProgress),
          websocketService.subscribe(EventType.TASK_COMPLETED, handleTaskProgress),
          websocketService.subscribe(EventType.TASK_FAILED, handleTaskProgress)
        );

        // 订阅Worker状态事件
        unsubscribeRefs.current.push(
          websocketService.subscribe(EventType.WORKER_ONLINE, handleWorkerStatus),
          websocketService.subscribe(EventType.WORKER_OFFLINE, handleWorkerStatus),
          websocketService.subscribe(EventType.WORKER_LOAD_CHANGED, handleWorkerStatus)
        );

        // 订阅URL状态事件
        unsubscribeRefs.current.push(
          websocketService.subscribe(EventType.URL_ASSIGNED, handleUrlStatus),
          websocketService.subscribe(EventType.URL_PROCESSING, handleUrlStatus),
          websocketService.subscribe(EventType.URL_COMPLETED, handleUrlStatus),
          websocketService.subscribe(EventType.URL_FAILED, handleUrlStatus)
        );

        // 订阅Worker分配事件
        unsubscribeRefs.current.push(
          websocketService.subscribe(EventType.ALLOCATION_UPDATED, handleWorkerAllocation)
        );

        // 如果指定了taskId，订阅特定任务
        if (taskId) {
          unsubscribeRefs.current.push(
            websocketService.subscribeToTask(taskId, (event) => {
              // 处理任务特定事件
              switch (event.event_type) {
                case EventType.TASK_PROGRESS:
                  handleTaskProgress(event);
                  break;
                case EventType.URL_ASSIGNED:
                case EventType.URL_PROCESSING:
                case EventType.URL_COMPLETED:
                case EventType.URL_FAILED:
                  handleUrlStatus(event);
                  break;
                case EventType.ALLOCATION_UPDATED:
                  handleWorkerAllocation(event);
                  break;
              }
            })
          );
        }

        // 如果指定了workerId，订阅特定Worker
        if (workerId) {
          unsubscribeRefs.current.push(
            websocketService.subscribeToWorker(workerId, handleWorkerStatus)
          );
        }

      } catch (error) {
        console.error('Failed to setup WebSocket subscriptions:', error);
        setState(prevState => ({
          ...prevState,
          connectionStatus: 'error'
        }));
      }
    };

    setupSubscriptions();

    return () => {
      // 清理订阅
      unsubscribeRefs.current.forEach(unsubscribe => unsubscribe());
      unsubscribeRefs.current = [];
    };
  }, [
    autoConnect,
    taskId,
    workerId,
    handleTaskProgress,
    handleWorkerStatus,
    handleUrlStatus,
    handleWorkerAllocation
  ]);

  // 手动连接函数
  const connect = useCallback(async () => {
    try {
      await websocketService.connect();
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      throw error;
    }
  }, []);

  // 断开连接函数
  const disconnect = useCallback(() => {
    websocketService.disconnect();
    unsubscribeRefs.current.forEach(unsubscribe => unsubscribe());
    unsubscribeRefs.current = [];
  }, []);

  // 获取特定任务的进度
  const getTaskProgress = useCallback((taskId: string): TaskProgress | undefined => {
    return state.taskProgress[taskId];
  }, [state.taskProgress]);

  // 获取特定Worker的状态
  const getWorkerStatus = useCallback((workerId: string): WorkerStatus | undefined => {
    return state.workerStatus[workerId];
  }, [state.workerStatus]);

  // 获取特定URL的状态
  const getUrlStatus = useCallback((urlId: string): UrlProcessingStatus | undefined => {
    return state.urlStatus[urlId];
  }, [state.urlStatus]);

  return {
    // 状态
    ...state,
    
    // 方法
    connect,
    disconnect,
    getTaskProgress,
    getWorkerStatus,
    getUrlStatus,
    
    // 计算属性
    isConnected: state.connectionStatus === 'connected',
    totalTasks: Object.keys(state.taskProgress).length,
    totalWorkers: Object.keys(state.workerStatus).length,
    totalUrls: Object.keys(state.urlStatus).length
  };
}
