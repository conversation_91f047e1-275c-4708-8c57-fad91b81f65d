"""
WebSocket路由
提供实时状态更新的WebSocket端点
"""

import uuid
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from app.websocket.websocket_manager import websocket_manager
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    connection_id: str = Query(default_factory=lambda: str(uuid.uuid4()))
):
    """
    WebSocket端点
    
    支持的客户端消息格式:
    - 订阅任务: {"action": "subscribe_task", "task_id": "task_id"}
    - 订阅Worker: {"action": "subscribe_worker", "worker_id": "worker_id"}
    - 订阅全局: {"action": "subscribe_global"}
    - 心跳: {"action": "ping"}
    """
    await websocket_manager.handle_websocket(websocket, connection_id)


@router.on_event("startup")
async def startup_websocket():
    """启动时初始化WebSocket管理器"""
    await websocket_manager.start()
    logger.info("WebSocket service started")


@router.on_event("shutdown")
async def shutdown_websocket():
    """关闭时清理WebSocket管理器"""
    await websocket_manager.stop()
    logger.info("WebSocket service stopped")
