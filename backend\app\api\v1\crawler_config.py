"""
爬虫配置管理API
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
import json
import os
from pathlib import Path

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from config.settings import Settings
import redis

settings = Settings()

def get_redis_client():
    """获取Redis客户端"""
    return redis.from_url(settings.REDIS_URL, decode_responses=True)

router = APIRouter()

class BrowserConfig(BaseModel):
    """浏览器配置"""
    headless: bool = True
    verbose: bool = False
    viewport_width: int = Field(1920, ge=800, le=3840)
    viewport_height: int = Field(1080, ge=600, le=2160)
    wait_for: float = Field(2.0, ge=0, le=30)
    timeout: int = Field(30, ge=5, le=300)
    ignore_https_errors: bool = True
    extra_args: List[str] = [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-blink-features=AutomationControlled",
        "--exclude-switches=enable-automation"
    ]

class CrawlerConfig(BaseModel):
    """爬虫核心配置"""
    method: str = "arun_many"
    verbose: bool = True
    check_robots_txt: bool = False
    fetch_ssl_certificate: bool = False
    simulate_user: bool = True
    magic: bool = True
    override_navigator: bool = True
    remove_overlay_elements: bool = True
    ignore_body_visibility: bool = True
    adjust_viewport_to_content: bool = True
    wait_until: str = Field("domcontentloaded", pattern="^(domcontentloaded|load|networkidle0|networkidle2)$")
    wait_for_images: bool = False
    page_timeout: int = Field(60000, ge=10000, le=300000)
    delay_before_return_html: float = Field(0.1, ge=0, le=10)
    js_only: bool = False
    scan_full_page: bool = True
    process_iframes: bool = True
    scroll_delay: float = Field(0.2, ge=0, le=5)
    cache_mode: str = Field("BYPASS", pattern="^(BYPASS|ENABLED|DISABLED)$")
    screenshot: bool = False
    pdf: bool = False
    capture_mhtml: bool = False
    exclude_external_images: bool = False
    exclude_all_images: bool = False
    image_score_threshold: int = Field(50, ge=0, le=100)
    image_description_min_word_threshold: int = Field(50, ge=0, le=200)
    table_score_threshold: int = Field(7, ge=0, le=20)
    capture_network_requests: bool = False
    capture_console_messages: bool = False
    log_console: bool = False
    extraction_strategy: str = "LLMExtractionStrategy"
    chunking_strategy: str = "IdentityChunking"
    markdown_generator: str = "DefaultMarkdownGenerator"
    bypass_cache: bool = True

class LLMConfig(BaseModel):
    """LLM配置"""
    query: str = "提取页面中所有文章的标题、作者和发布时间"
    provider: str = Field("openai", pattern="^(openai|anthropic|google)$")
    model: str = "deepseek-v3-0324"
    api_key: str = Field(default="", description="API密钥")
    base_url: str = "https://api.lkeap.cloud.tencent.com/v1"
    temperature: float = Field(0.0, ge=0, le=2)
    max_tokens: int = Field(16384, ge=100, le=32768)
    top_p: float = Field(0.9, ge=0, le=1)

class SchemaExtractionConfig(BaseModel):
    """Schema提取配置"""
    enabled: bool = True
    schema_type: str = "auto"
    extraction_schema: dict = {}
    instructions: str = ""
    validate_schema: bool = True
    return_raw: bool = False

class ContentProcessingConfig(BaseModel):
    """内容处理配置"""
    word_count_threshold: int = Field(200, ge=0, le=10000)
    css_selector: str = ""
    target_elements: List[str] = []
    excluded_tags: List[str] = ["nav", "footer", "aside"]
    excluded_selector: str = ""
    remove_forms: bool = False
    only_text: bool = False
    prettify: bool = False
    parser_type: str = "lxml"
    keep_data_attributes: bool = False
    keep_attrs: List[str] = []

class LinkFilteringConfig(BaseModel):
    """链接过滤配置"""
    exclude_external_links: bool = False
    exclude_internal_links: bool = False
    exclude_social_media_links: bool = False
    exclude_domains: List[str] = ["example.com", "ads.google.com", "facebook.com", "twitter.com"]
    social_media_domains: List[str] = [
        "facebook.com", "twitter.com", "instagram.com", "linkedin.com",
        "youtube.com", "tiktok.com", "pinterest.com", "reddit.com"
    ]
    exclude_external_images: bool = False
    exclude_all_images: bool = False
    image_score_threshold: int = Field(3, ge=0, le=100)
    image_description_min_word_threshold: int = Field(50, ge=0, le=200)
    table_score_threshold: int = Field(7, ge=0, le=20)

class SchedulerConfig(BaseModel):
    """调度器配置"""
    semaphore_count: int = Field(2, ge=1, le=20)
    mean_delay: float = Field(0.1, ge=0, le=10)
    max_range: int = Field(3, ge=1, le=10)
    pool_size: int = Field(10, ge=5, le=50)
    memory_threshold: int = Field(4096, ge=1024, le=16384)

class MonitorConfig(BaseModel):
    """监控配置"""
    display_mode: str = Field("detailed", pattern="^(simple|detailed|debug)$")
    show_progress: bool = True
    log_errors: bool = True

class APIConfig(BaseModel):
    """API配置"""
    base_url: str = "http://localhost:11234"
    timeout: int = Field(30000, ge=1000, le=300000)
    max_retries: int = Field(3, ge=0, le=10)

class CrawlerFullConfig(BaseModel):
    """完整的爬虫配置"""
    api: APIConfig
    browser: BrowserConfig
    crawler: CrawlerConfig
    llm: LLMConfig
    schema_extraction: SchemaExtractionConfig
    content_processing: ContentProcessingConfig
    link_filtering: LinkFilteringConfig
    scheduler: SchedulerConfig
    monitor: MonitorConfig

    @validator('llm')
    def validate_llm_config(cls, v):
        """验证LLM配置"""
        # 允许API密钥为空，在实际使用时再验证
        if v.api_key and len(v.api_key.strip()) > 0 and len(v.api_key.strip()) < 10:
            raise ValueError("API密钥长度至少10个字符")
        return v

class CrawlerConfigService:
    """爬虫配置服务"""
    
    def __init__(self):
        self.redis_client = get_redis_client()
        self.config_key = "crawler:config"
        # 创建数据目录
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        self.config_file = data_dir / "crawler_config.json"
        
    async def get_config(self) -> CrawlerFullConfig:
        """获取爬虫配置"""
        try:
            # 优先从Redis获取
            config_data = self.redis_client.get(self.config_key)
            if config_data:
                config_dict = json.loads(config_data)
                return CrawlerFullConfig(**config_dict)

            # 从文件获取
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                    config = CrawlerFullConfig(**config_dict)
                    # 同步到Redis
                    await self.save_config(config)
                    return config

            # 返回默认配置
            return self._get_default_config()

        except Exception as e:
            print(f"获取配置失败: {e}")
            return self._get_default_config()
    
    async def save_config(self, config: CrawlerFullConfig) -> bool:
        """保存爬虫配置"""
        try:
            config_dict = config.dict()

            # 保存到Redis
            self.redis_client.set(
                self.config_key,
                json.dumps(config_dict, ensure_ascii=False),
                ex=86400 * 30  # 30天过期
            )

            # 保存到文件
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, ensure_ascii=False, indent=2)

            return True

        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def _get_default_config(self) -> CrawlerFullConfig:
        """获取默认配置"""
        # 默认的电商商品信息提取Schema
        default_extraction_schema = {
            "type": "object",
            "description": "电商商品完整信息提取结构",
            "properties": {
                "task_info": {
                    "type": "object",
                    "description": "任务执行信息",
                    "properties": {
                        "start_timestamp": {
                            "type": "string",
                            "pattern": "^[0-9]{14}$",
                            "description": "任务开始的时间戳，格式：YYYYMMDDHHMMSS"
                        }
                    },
                    "required": ["start_timestamp"]
                },
                "product_basic_info": {
                    "type": "object",
                    "description": "商品基础信息",
                    "properties": {
                        "product_url": {
                            "type": "string",
                            "format": "uri",
                            "description": "商品链接"
                        },
                        "product_name": {
                            "type": "string",
                            "description": "商品名称"
                        },
                        "mlm_id": {
                            "type": "number",
                            "description": "商品MLM-ID中的数值部分"
                        }
                    },
                    "required": ["product_url", "product_name", "mlm_id"]
                },
                "pricing_info": {
                    "type": "object",
                    "description": "价格和库存信息",
                    "properties": {
                        "sales_count": {
                            "type": "number",
                            "description": "商品销量（格式：+xxx vendidos中的数值）"
                        },
                        "current_price": {
                            "type": "number",
                            "description": "商品现价（数值）"
                        },
                        "original_price": {
                            "type": "number",
                            "description": "商品原价（数值），如果只有一个价格，或者没打折，商品现价（数值）就是商品原价"
                        },
                        "discount_rate": {
                            "type": "number",
                            "description": "商品折扣率（如：33% OFF），如果没打折，商品折扣率就是100"
                        },
                        "stock_quantity": {
                            "type": "number",
                            "description": "商品库存数量，(+50 disponibles) 中的数值，如果页面显示Publicación pausada或者没有获取到(+50 disponibles)中的数值，那库存数量就是0"
                        },
                        "stocktype_IsFull": {
                            "type": "number",
                            "description": "商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\":\"Full\"，商品有就是1，没有就是0"
                        }
                    },
                    "required": ["current_price"]
                },
                "category_info": {
                    "type": "object",
                    "description": "商品目录分类信息",
                    "properties": {
                        "category_breadcrumb": {
                            "type": "string",
                            "description": "商品各级目录文本（例如：Herramientas > Cajas y Organizadores > Bolsas Portaherramientas）"
                        },
                        "category_hierarchy": {
                            "type": "array",
                            "description": "商品各级目录详细信息",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "category_name": {
                                        "type": "string",
                                        "description": "目录名称"
                                    },
                                    "category_url": {
                                        "type": "string",
                                        "format": "uri",
                                        "description": "目录链接"
                                    },
                                    "level": {
                                        "type": "integer",
                                        "description": "目录层级（1为顶级，2为二级，以此类推）"
                                    }
                                },
                                "required": ["category_name", "level"]
                            }
                        }
                    },
                    "required": ["category_breadcrumb"]
                },
                "seller_info": {
                    "type": "object",
                    "description": "销售商信息",
                    "properties": {
                        "seller_name": {
                            "type": "string",
                            "description": "商品销售商名称"
                        },
                        "seller_url": {
                            "type": "string",
                            "format": "uri",
                            "description": "商品销售商链接"
                        }
                    },
                    "required": ["seller_name"]
                },
                "media_info": {
                    "type": "object",
                    "description": "商品媒体信息",
                    "properties": {
                        "main_image_url": {
                            "type": "string",
                            "format": "uri",
                            "description": "商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）"
                        }
                    },
                    "required": ["main_image_url"]
                },
                "qa_section": {
                    "type": "object",
                    "description": "问答区域信息",
                    "properties": {
                        "questions": {
                            "type": "array",
                            "description": "商品页面的问题列表",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "question_content": {
                                        "type": "string",
                                        "description": "问题内容"
                                    },
                                    "question_time": {
                                        "type": "string",
                                        "description": "问题时间"
                                    },
                                    "answer_content": {
                                        "type": "string",
                                        "description": "问题回答"
                                    }
                                },
                                "required": ["question_content", "answer_content"]
                            }
                        },
                        "all_questions_url": {
                            "type": "string",
                            "format": "uri",
                            "description": "商品所有问题页面的链接（Ver todas las preguntas），类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接"
                        }
                    }
                },
                "rating_info": {
                    "type": "object",
                    "description": "评分信息",
                    "properties": {
                        "rating_score": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 5,
                            "description": "商品评分数（0-5分）"
                        },
                        "rating_count": {
                            "type": "integer",
                            "description": "商品评分数量"
                        }
                    },
                    "required": ["rating_score", "rating_count"]
                },
                "reviews_section": {
                    "type": "object",
                    "description": "评论区域信息",
                    "properties": {
                        "reviews": {
                            "type": "array",
                            "description": "商品评论列表",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "review_content": {
                                        "type": "string",
                                        "description": "评论内容"
                                    },
                                    "review_rating": {
                                        "type": "number",
                                        "minimum": 0,
                                        "maximum": 5,
                                        "description": "评论评分（0-5分）"
                                    },
                                    "review_time": {
                                        "type": "string",
                                        "description": "评论时间"
                                    }
                                },
                                "required": ["review_content", "review_time"]
                            }
                        },
                        "all_reviews_url": {
                            "type": "string",
                            "format": "uri",
                            "description": "Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接"
                        }
                    }
                }
            },
            "required": [
                "product_basic_info",
                "category_info",
                "pricing_info",
                "media_info",
                "rating_info"
            ]
        }

        default_instructions = """请严格按照以下条件提取商品信息：
1. 只提取页面主要展示的商品信息，忽略推荐商品、相关商品
2. 如果页面有多个商品，只提取最突出显示的主商品
3. 重点关注页面标题中提到的商品
4. 忽略广告推荐和次要商品信息
5. 确保提取的商品名称与页面URL或页面标题相匹配
6. 确保提取的商品mlmid与页面URL的mlmid相匹配

需要提取的信息如下：
商品链接、商品名称、商品MLM-ID；
商品销量（格式  +xxx vendidos）、商品原价、商品现价、商品折扣率、商品库存数量、商品是否属于FULL仓配送商品(商品是否属于FULL仓配送商品，判断商品信息否有类似ui-pdp-icon ui-pdp-icon--full ui-pdp-color--GREEN或者full_icon或者fulfillment_information或者accessibility_text\\":\\"Full\\"，商品有就是1，没有就是0)；
获取商品的各级目录文本（例如 Herramientas> Cajas y Organizadores > Bolsas Portaherramientas）、获取商品各级目录的链接；
商品销售商名称、商品销售商链接；
商品第一张图片的最大尺寸版链接（ 类似https://http2.mlstatic.com/D_NQ_NP_2X_926933-MLM74147511485_012024-F-bolsas-portaherramientas-cangurera-para-electricistas-tool.webp）；
商品页面的问题（Preguntas y respuestas）相关的内容、问题时间、问题回答；
商品所有问题页面的链接（Ver todas las preguntas，类似https://articulo.mercadolibre.com.mx/noindex/questions/MLM2830525754?scroll_to_question=2&new_version=true&modal=false&parent_origin=undefined，如果商品页还没有问题就没有对应的链接）；
商品评分数、商品评分数量、
商品的评论内容、评论评分、评论时间；
商品所有评论页面的链接（Mostrar todas las opiniones，类似https://articulo.mercadolibre.com.mx/noindex/catalog/reviews/MLM1935037877?noIndex=true&access=view_all&modal=true&sourcePlatform=/web/desktop，如果商品页还没有评论就没有对应的链接）；
任务开始的时间戳YYYYMMDDHHMMSS"""

        return CrawlerFullConfig(
            api=APIConfig(),
            browser=BrowserConfig(),
            crawler=CrawlerConfig(),
            llm=LLMConfig(
                api_key="sk-6wwriVXlcG3pPNuqcf9z55afu5RImH9AZZZJxBbHxE0KHUhi"  # 使用示例中的API密钥
            ),
            schema_extraction=SchemaExtractionConfig(
                enabled=True,
                schema_type="auto",
                extraction_schema=default_extraction_schema,
                instructions=default_instructions,
                validate_schema=True,
                return_raw=False
            ),
            content_processing=ContentProcessingConfig(),
            link_filtering=LinkFilteringConfig(),
            scheduler=SchedulerConfig(),
            monitor=MonitorConfig()
        )
    
    def build_crawl4ai_request(self, config: CrawlerFullConfig, urls: List[str]) -> Dict[str, Any]:
        """构建Crawl4AI API请求"""
        return {
            "urls": urls,
            "browser_config": {
                "headless": config.browser.headless,
                "verbose": config.browser.verbose,
                "viewport_width": config.browser.viewport_width,
                "viewport_height": config.browser.viewport_height,
                "wait_for": config.browser.wait_for,
                "timeout": config.browser.timeout,
                "ignore_https_errors": config.browser.ignore_https_errors,
                "extra_args": config.browser.extra_args
            },
            "crawler_config": {
                "method": config.crawler.method,
                "verbose": config.crawler.verbose,
                "check_robots_txt": config.crawler.check_robots_txt,
                "fetch_ssl_certificate": config.crawler.fetch_ssl_certificate,
                "simulate_user": config.crawler.simulate_user,
                "magic": config.crawler.magic,
                "override_navigator": config.crawler.override_navigator,
                "remove_overlay_elements": config.crawler.remove_overlay_elements,
                "ignore_body_visibility": config.crawler.ignore_body_visibility,
                "adjust_viewport_to_content": config.crawler.adjust_viewport_to_content,
                "wait_until": config.crawler.wait_until,
                "wait_for_images": config.crawler.wait_for_images,
                "page_timeout": config.crawler.page_timeout,
                "delay_before_return_html": config.crawler.delay_before_return_html,
                "js_only": config.crawler.js_only,
                "scan_full_page": config.crawler.scan_full_page,
                "process_iframes": config.crawler.process_iframes,
                "scroll_delay": config.crawler.scroll_delay,
                "cache_mode": config.crawler.cache_mode,
                "screenshot": config.crawler.screenshot,
                "pdf": config.crawler.pdf,
                "capture_mhtml": config.crawler.capture_mhtml,
                "exclude_external_images": config.crawler.exclude_external_images,
                "exclude_all_images": config.crawler.exclude_all_images,
                "image_score_threshold": config.crawler.image_score_threshold,
                "image_description_min_word_threshold": config.crawler.image_description_min_word_threshold,
                "table_score_threshold": config.crawler.table_score_threshold,
                "capture_network_requests": config.crawler.capture_network_requests,
                "capture_console_messages": config.crawler.capture_console_messages,
                "log_console": config.crawler.log_console,
                "extraction_strategy": config.crawler.extraction_strategy,
                "chunking_strategy": config.crawler.chunking_strategy,
                "markdown_generator": config.crawler.markdown_generator,
                "bypass_cache": config.crawler.bypass_cache,
                "llm_extraction": {
                    "query": config.llm.query,
                    "provider": config.llm.provider,
                    "model": config.llm.model,
                    "api_key": config.llm.api_key,
                    "base_url": config.llm.base_url,
                    "temperature": config.llm.temperature,
                    "max_tokens": config.llm.max_tokens,
                    "top_p": config.llm.top_p
                },
                "schema_extraction": {
                    "schema": config.schema_extraction.extraction_schema,
                    "instructions": config.schema_extraction.instructions,
                    "validate_schema": config.schema_extraction.validate_schema,
                    "return_raw": config.schema_extraction.return_raw
                },
                "content_processing": {
                    "word_count_threshold": config.content_processing.word_count_threshold,
                    "css_selector": config.content_processing.css_selector,
                    "target_elements": config.content_processing.target_elements,
                    "excluded_tags": config.content_processing.excluded_tags,
                    "excluded_selector": config.content_processing.excluded_selector,
                    "remove_forms": config.content_processing.remove_forms,
                    "only_text": config.content_processing.only_text,
                    "prettify": config.content_processing.prettify,
                    "parser_type": config.content_processing.parser_type,
                    "keep_data_attributes": config.content_processing.keep_data_attributes,
                    "keep_attrs": config.content_processing.keep_attrs
                },
                "link_filtering": {
                    "exclude_external_links": config.link_filtering.exclude_external_links,
                    "exclude_internal_links": config.link_filtering.exclude_internal_links,
                    "exclude_social_media_links": config.link_filtering.exclude_social_media_links,
                    "exclude_domains": config.link_filtering.exclude_domains,
                    "social_media_domains": config.link_filtering.social_media_domains,
                    "exclude_external_images": config.link_filtering.exclude_external_images,
                    "exclude_all_images": config.link_filtering.exclude_all_images,
                    "image_score_threshold": config.link_filtering.image_score_threshold,
                    "image_description_min_word_threshold": config.link_filtering.image_description_min_word_threshold,
                    "table_score_threshold": config.link_filtering.table_score_threshold
                },
                "_config_summary": {
                    "enabled_features": {
                        "llm_extraction": True,
                        "css_extraction": False,
                        "schema_extraction": True,
                        "content_processing": True,
                        "link_filtering": True
                    },
                    "strategy_used": config.crawler.extraction_strategy
                },
                "semaphore_count": config.scheduler.semaphore_count,
                "stream": True,
                "mean_delay": config.scheduler.mean_delay,
                "max_range": config.scheduler.max_range,
                "task_type": "batch",
                "scheduler": {
                    "type": "AsyncQueueManager",
                    "pool_size": config.scheduler.pool_size,
                    "memory_threshold": config.scheduler.memory_threshold
                },
                "monitor": {
                    "display_mode": config.monitor.display_mode,
                    "show_progress": config.monitor.show_progress,
                    "log_errors": config.monitor.log_errors
                }
            }
        }

# 全局配置服务实例
config_service = CrawlerConfigService()

@router.get("/config", response_model=CrawlerFullConfig)
async def get_crawler_config():
    """获取爬虫配置"""
    try:
        # 直接从配置服务获取
        config = await config_service.get_config()

        # 隐藏敏感信息
        config.llm.api_key = "***" + config.llm.api_key[-4:] if len(config.llm.api_key) > 4 else "***"
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")

@router.get("/config/default", response_model=CrawlerFullConfig)
async def get_default_crawler_config():
    """获取默认爬虫配置"""
    try:
        config = config_service._get_default_config()
        # 不隐藏API密钥，因为这是默认配置
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取默认配置失败: {str(e)}")

@router.put("/config")
async def update_crawler_config(config: CrawlerFullConfig):
    """更新爬虫配置（兼容模式：更新默认爬虫实例配置）"""
    try:
        # 尝试更新爬虫实例配置
        try:
            from ...services.crawler_instance_config_service import crawler_instance_config_service
            from ...schemas.crawler_instance_config import CrawlerInstanceConfigUpdate, AuthConfig, AuthType

            # 获取配置管理器
            manager = await crawler_instance_config_service.get_config_manager()
            default_config = manager.get_default_config()

            if default_config:
                # 更新默认配置
                update_data = CrawlerInstanceConfigUpdate(
                    api_endpoint=config.api.base_url,
                    timeout=config.api.timeout,
                    max_retries=config.api.max_retries,
                    browser=config.browser,
                    crawler=config.crawler,
                    llm=config.llm,
                    schema_extraction=config.schema_extraction,
                    content_processing=config.content_processing,
                    link_filtering=config.link_filtering,
                    scheduler=config.scheduler,
                    monitor=config.monitor
                )

                await crawler_instance_config_service.update_config(default_config.config_id, update_data)
            else:
                # 如果没有默认配置，创建一个
                from ...schemas.crawler_instance_config import CrawlerInstanceConfigCreate

                create_data = CrawlerInstanceConfigCreate(
                    config_name="默认爬虫配置",
                    description="从单一配置创建的默认配置",
                    api_endpoint=config.api.base_url,
                    timeout=config.api.timeout,
                    max_retries=config.api.max_retries,
                    auth_config=AuthConfig(auth_type=AuthType.NONE),
                    browser=config.browser,
                    crawler=config.crawler,
                    llm=config.llm,
                    schema_extraction=config.schema_extraction,
                    content_processing=config.content_processing,
                    link_filtering=config.link_filtering,
                    scheduler=config.scheduler,
                    monitor=config.monitor
                )

                await crawler_instance_config_service.create_config(create_data)

        except Exception as instance_error:
            # 如果实例配置更新失败，回退到原有方式
            print(f"Instance config update failed, falling back to single config: {instance_error}")
            success = await config_service.save_config(config)
            if not success:
                raise HTTPException(status_code=500, detail="配置保存失败")

        return {"message": "配置保存成功"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存配置失败: {str(e)}")

def _resolve_api_url(base_url: str) -> list[str]:
    """解析API URL，处理Docker环境中的localhost问题"""
    import os
    from urllib.parse import urlparse, urlunparse

    parsed = urlparse(base_url)

    # 如果不是localhost，直接返回原URL
    if parsed.hostname not in ['localhost', '127.0.0.1']:
        return [base_url]

    # 检测是否在Docker环境中
    in_docker = os.path.exists('/.dockerenv') or os.path.exists('/proc/1/cgroup')

    urls_to_try = [base_url]  # 总是先尝试原始URL

    if in_docker:
        # 在Docker中，尝试host.docker.internal
        docker_url = urlunparse((
            parsed.scheme,
            f"host.docker.internal:{parsed.port}",
            parsed.path,
            parsed.params,
            parsed.query,
            parsed.fragment
        ))
        urls_to_try.append(docker_url)

        # 也尝试**********（Docker默认网关）
        gateway_url = urlunparse((
            parsed.scheme,
            f"**********:{parsed.port}",
            parsed.path,
            parsed.params,
            parsed.query,
            parsed.fragment
        ))
        urls_to_try.append(gateway_url)

    return urls_to_try

@router.post("/test-connection")
async def test_crawler_connection():
    """测试爬虫连接"""
    try:
        config = await config_service.get_config()

        # 实际测试连接到Crawl4AI服务
        import httpx
        import asyncio

        test_results = {
            "api_server": {"status": "unknown", "message": "", "response_time": 0},
            "llm_service": {"status": "unknown", "message": "", "response_time": 0},
            "overall": {"status": "unknown", "message": ""}
        }

        # 1. 测试Crawl4AI API服务器连接
        try:
            # 配置httpx客户端
            async with httpx.AsyncClient(
                timeout=10.0,
                verify=False,  # 禁用SSL验证
                follow_redirects=True  # 允许重定向
            ) as client:
                # 获取要尝试的URL列表（处理Docker环境）
                base_urls = _resolve_api_url(config.api.base_url)
                health_endpoints = ["/health", "/", "/api/health", "/status"]
                api_connected = False
                last_error = None
                successful_url = None

                # 尝试每个可能的基础URL
                for base_url in base_urls:
                    if api_connected:
                        break

                    for endpoint in health_endpoints:
                        try:
                            start_time = asyncio.get_event_loop().time()
                            full_url = f"{base_url}{endpoint}"

                            response = await client.get(full_url)
                            end_time = asyncio.get_event_loop().time()
                            response_time = round((end_time - start_time) * 1000, 2)

                            if response.status_code in [200, 404]:  # 404也表示服务器在运行
                                successful_url = base_url
                                # 尝试解析响应内容
                                try:
                                    response_data = response.json()
                                    if response_data.get("status") == "ok":
                                        version = response_data.get("version", "unknown")
                                        message = f"API服务器连接成功 (v{version})"
                                        if base_url != config.api.base_url:
                                            message += f" [通过 {base_url}]"
                                        test_results["api_server"] = {
                                            "status": "success",
                                            "message": message,
                                            "response_time": response_time
                                        }
                                    else:
                                        message = f"API服务器连接成功 (HTTP {response.status_code})"
                                        if base_url != config.api.base_url:
                                            message += f" [通过 {base_url}]"
                                        test_results["api_server"] = {
                                            "status": "success",
                                            "message": message,
                                            "response_time": response_time
                                        }
                                except:
                                    message = f"API服务器连接成功 (HTTP {response.status_code})"
                                    if base_url != config.api.base_url:
                                        message += f" [通过 {base_url}]"
                                    test_results["api_server"] = {
                                        "status": "success",
                                        "message": message,
                                        "response_time": response_time
                                    }
                                api_connected = True
                                break
                            else:
                                last_error = f"HTTP {response.status_code}"

                        except httpx.TimeoutException:
                            last_error = "连接超时"
                            continue
                        except httpx.ConnectError:
                            last_error = "连接被拒绝"
                            continue
                        except httpx.RequestError as e:
                            last_error = f"请求错误: {str(e)}"
                            continue
                        except Exception as e:
                            last_error = f"未知错误: {str(e)}"
                            continue

                if not api_connected:
                    # 构建尝试过的URL列表信息
                    tried_urls = ", ".join(base_urls)
                    test_results["api_server"] = {
                        "status": "error",
                        "message": f"无法连接到API服务器 - 尝试了: {tried_urls} - {last_error or '所有端点都无法访问'}",
                        "response_time": 0
                    }

        except Exception as e:
            test_results["api_server"] = {
                "status": "error",
                "message": f"API服务器测试失败: {str(e)}",
                "response_time": 0
            }

        # 2. 测试LLM服务连接
        try:
            start_time = asyncio.get_event_loop().time()
            async with httpx.AsyncClient(timeout=15.0) as client:
                # 测试LLM API连接
                headers = {
                    "Authorization": f"Bearer {config.llm.api_key}",
                    "Content-Type": "application/json"
                }

                # 发送一个简单的测试请求
                test_payload = {
                    "model": config.llm.model,
                    "messages": [{"role": "user", "content": "test"}],
                    "max_tokens": 1,
                    "temperature": 0
                }

                response = await client.post(
                    f"{config.llm.base_url}/chat/completions",
                    json=test_payload,
                    headers=headers
                )

                end_time = asyncio.get_event_loop().time()
                response_time = round((end_time - start_time) * 1000, 2)

                if response.status_code == 200:
                    test_results["llm_service"] = {
                        "status": "success",
                        "message": f"LLM服务连接成功 ({config.llm.model})",
                        "response_time": response_time
                    }
                elif response.status_code == 401:
                    test_results["llm_service"] = {
                        "status": "error",
                        "message": "LLM API密钥无效或已过期",
                        "response_time": response_time
                    }
                elif response.status_code == 429:
                    test_results["llm_service"] = {
                        "status": "warning",
                        "message": "LLM服务连接成功，但达到速率限制",
                        "response_time": response_time
                    }
                else:
                    error_text = response.text[:200] if hasattr(response, 'text') else "未知错误"
                    test_results["llm_service"] = {
                        "status": "error",
                        "message": f"LLM服务返回错误 (HTTP {response.status_code}): {error_text}",
                        "response_time": response_time
                    }

        except httpx.TimeoutException:
            test_results["llm_service"] = {
                "status": "error",
                "message": "LLM服务连接超时 (>15秒)",
                "response_time": 0
            }
        except Exception as e:
            test_results["llm_service"] = {
                "status": "error",
                "message": f"LLM服务连接失败: {str(e)}",
                "response_time": 0
            }

        # 3. 综合评估
        api_ok = test_results["api_server"]["status"] == "success"
        llm_ok = test_results["llm_service"]["status"] in ["success", "warning"]

        if api_ok and llm_ok:
            test_results["overall"] = {
                "status": "success",
                "message": "所有服务连接正常，系统可以正常工作"
            }
        elif api_ok and not llm_ok:
            test_results["overall"] = {
                "status": "warning",
                "message": "API服务器正常，但LLM服务有问题，可能影响数据提取功能"
            }
        elif not api_ok and llm_ok:
            test_results["overall"] = {
                "status": "warning",
                "message": "LLM服务正常，但API服务器连接失败，无法执行爬虫任务"
            }
        else:
            test_results["overall"] = {
                "status": "error",
                "message": "所有服务连接失败，系统无法正常工作"
            }

        return test_results

    except Exception as e:
        return {
            "api_server": {"status": "error", "message": f"测试过程出错: {str(e)}", "response_time": 0},
            "llm_service": {"status": "error", "message": "未测试", "response_time": 0},
            "overall": {"status": "error", "message": f"连接测试失败: {str(e)}"}
        }

@router.post("/build-request")
async def build_crawl_request(urls: List[str]):
    """构建爬虫请求"""
    try:
        config = await config_service.get_config()
        request_data = config_service.build_crawl4ai_request(config, urls)
        return request_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"构建请求失败: {str(e)}")
