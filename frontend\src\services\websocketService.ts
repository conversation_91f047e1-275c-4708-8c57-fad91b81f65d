/**
 * WebSocket服务
 * 提供实时状态更新功能
 */

export enum EventType {
  // 系统级事件
  WORKER_ONLINE = 'worker_online',
  WORKER_OFFLINE = 'worker_offline',
  SYSTEM_OVERLOAD = 'system_overload',
  
  // 任务级事件
  TASK_STARTED = 'task_started',
  TASK_PROGRESS = 'task_progress',
  TASK_COMPLETED = 'task_completed',
  TASK_FAILED = 'task_failed',
  TASK_PAUSED = 'task_paused',
  TASK_RESUMED = 'task_resumed',
  
  // URL级事件
  URL_ASSIGNED = 'url_assigned',
  URL_PROCESSING = 'url_processing',
  URL_COMPLETED = 'url_completed',
  URL_FAILED = 'url_failed',
  
  // Worker分配事件
  WORKER_ASSIGNED = 'worker_assigned',
  WORKER_LOAD_CHANGED = 'worker_load_changed',
  ALLOCATION_UPDATED = 'allocation_updated'
}

export interface WebSocketEvent {
  event_type: EventType;
  data: Record<string, any>;
  timestamp: string;
  task_id?: string;
  worker_id?: string;
  url_id?: string;
}

export type EventHandler = (event: WebSocketEvent) => void;

export interface SubscriptionOptions {
  taskId?: string;
  workerId?: string;
  global?: boolean;
}

class WebSocketService {
  private ws: WebSocket | null = null;
  private connectionId: string;
  private eventHandlers: Map<EventType, Set<EventHandler>> = new Map();
  private globalHandlers: Set<EventHandler> = new Set();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private subscriptions: SubscriptionOptions = {};
  
  constructor() {
    this.connectionId = this.generateConnectionId();
  }
  
  private generateConnectionId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }
    
    this.isConnecting = true;
    
    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;
      const wsUrl = `${protocol}//${host}/api/ws?connection_id=${this.connectionId}`;
      
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);
      
      // 等待连接建立
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, 5000);
        
        if (this.ws) {
          this.ws.addEventListener('open', () => {
            clearTimeout(timeout);
            resolve();
          });
          
          this.ws.addEventListener('error', () => {
            clearTimeout(timeout);
            reject(new Error('WebSocket connection failed'));
          });
        }
      });
      
    } catch (error) {
      this.isConnecting = false;
      throw error;
    }
  }
  
  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }
  
  /**
   * 订阅事件
   */
  subscribe(eventType: EventType, handler: EventHandler): () => void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set());
    }
    
    this.eventHandlers.get(eventType)!.add(handler);
    
    // 返回取消订阅函数
    return () => {
      const handlers = this.eventHandlers.get(eventType);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          this.eventHandlers.delete(eventType);
        }
      }
    };
  }
  
  /**
   * 订阅全局事件
   */
  subscribeGlobal(handler: EventHandler): () => void {
    this.globalHandlers.add(handler);
    
    // 发送全局订阅消息
    this.sendMessage({
      action: 'subscribe_global'
    });
    
    return () => {
      this.globalHandlers.delete(handler);
    };
  }
  
  /**
   * 订阅任务事件
   */
  subscribeToTask(taskId: string, handler: EventHandler): () => void {
    const unsubscribe = this.subscribe(EventType.TASK_PROGRESS, handler);
    
    // 发送任务订阅消息
    this.sendMessage({
      action: 'subscribe_task',
      task_id: taskId
    });
    
    this.subscriptions.taskId = taskId;
    
    return unsubscribe;
  }
  
  /**
   * 订阅Worker事件
   */
  subscribeToWorker(workerId: string, handler: EventHandler): () => void {
    const unsubscribe = this.subscribe(EventType.WORKER_LOAD_CHANGED, handler);
    
    // 发送Worker订阅消息
    this.sendMessage({
      action: 'subscribe_worker',
      worker_id: workerId
    });
    
    this.subscriptions.workerId = workerId;
    
    return unsubscribe;
  }
  
  /**
   * 发送消息
   */
  private sendMessage(message: Record<string, any>): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }
  
  /**
   * 处理连接打开
   */
  private handleOpen(): void {
    console.log('WebSocket connected');
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    
    // 重新订阅
    this.resubscribe();
    
    // 发送心跳
    this.startHeartbeat();
  }
  
  /**
   * 处理消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const wsEvent: WebSocketEvent = JSON.parse(event.data);
      
      // 处理心跳响应
      if (wsEvent.data?.type === 'pong') {
        return;
      }
      
      // 触发特定事件处理器
      const handlers = this.eventHandlers.get(wsEvent.event_type);
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(wsEvent);
          } catch (error) {
            console.error('Error in event handler:', error);
          }
        });
      }
      
      // 触发全局事件处理器
      this.globalHandlers.forEach(handler => {
        try {
          handler(wsEvent);
        } catch (error) {
          console.error('Error in global event handler:', error);
        }
      });
      
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }
  
  /**
   * 处理连接关闭
   */
  private handleClose(): void {
    console.log('WebSocket disconnected');
    this.isConnecting = false;
    
    // 尝试重连
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
      
      setTimeout(() => {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }
  
  /**
   * 处理连接错误
   */
  private handleError(error: Event): void {
    console.error('WebSocket error:', error);
  }
  
  /**
   * 重新订阅
   */
  private resubscribe(): void {
    if (this.subscriptions.global) {
      this.sendMessage({ action: 'subscribe_global' });
    }
    
    if (this.subscriptions.taskId) {
      this.sendMessage({
        action: 'subscribe_task',
        task_id: this.subscriptions.taskId
      });
    }
    
    if (this.subscriptions.workerId) {
      this.sendMessage({
        action: 'subscribe_worker',
        worker_id: this.subscriptions.workerId
      });
    }
  }
  
  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.sendMessage({ action: 'ping' });
      }
    }, 30000); // 30秒心跳
  }
  
  /**
   * 获取连接状态
   */
  get isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }
  
  /**
   * 获取连接状态文本
   */
  get connectionState(): string {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'closed';
      default:
        return 'unknown';
    }
  }
}

// 导出单例实例
export const websocketService = new WebSocketService();

// 自动连接
websocketService.connect().catch(error => {
  console.error('Failed to establish WebSocket connection:', error);
});
