/**
 * WebSocket连接测试页面
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Tag, Space, Typography, List, Alert } from 'antd';
import { useRealTimeUpdates } from '../hooks/useRealTimeUpdates';

const { Title, Text } = Typography;

const WebSocketTest: React.FC = () => {
  const [events, setEvents] = useState<any[]>([]);
  
  const {
    connectionStatus,
    isConnected,
    connect,
    disconnect
  } = useRealTimeUpdates({
    onTaskProgress: (progress) => {
      setEvents(prev => [...prev, { type: 'task_progress', data: progress, timestamp: new Date().toISOString() }]);
    },
    onWorkerStatusChange: (status) => {
      setEvents(prev => [...prev, { type: 'worker_status', data: status, timestamp: new Date().toISOString() }]);
    },
    onUrlStatusChange: (status) => {
      setEvents(prev => [...prev, { type: 'url_status', data: status, timestamp: new Date().toISOString() }]);
    },
    onConnectionChange: (status) => {
      setEvents(prev => [...prev, { type: 'connection', data: { status }, timestamp: new Date().toISOString() }]);
    }
  });

  const handleConnect = async () => {
    try {
      await connect();
    } catch (error) {
      console.error('连接失败:', error);
    }
  };

  const handleDisconnect = () => {
    disconnect();
  };

  const clearEvents = () => {
    setEvents([]);
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>WebSocket连接测试</Title>
      
      <Card title="连接状态" style={{ marginBottom: 16 }}>
        <Space>
          <Text>状态:</Text>
          <Tag color={isConnected ? 'green' : 'red'}>
            {connectionStatus}
          </Tag>
          
          <Button 
            type="primary" 
            onClick={handleConnect}
            disabled={isConnected}
          >
            连接
          </Button>
          
          <Button 
            onClick={handleDisconnect}
            disabled={!isConnected}
          >
            断开
          </Button>
          
          <Button onClick={clearEvents}>
            清空事件
          </Button>
        </Space>
      </Card>

      <Card title="实时事件" style={{ marginBottom: 16 }}>
        {events.length === 0 ? (
          <Alert message="暂无事件" type="info" />
        ) : (
          <List
            dataSource={events.slice(-10)} // 只显示最近10个事件
            renderItem={(event, index) => (
              <List.Item key={index}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Space>
                    <Tag color="blue">{event.type}</Tag>
                    <Text type="secondary">{new Date(event.timestamp).toLocaleTimeString()}</Text>
                  </Space>
                  <Text code style={{ fontSize: '12px' }}>
                    {JSON.stringify(event.data, null, 2)}
                  </Text>
                </Space>
              </List.Item>
            )}
          />
        )}
      </Card>

      <Card title="使用说明">
        <ul>
          <li>点击"连接"按钮建立WebSocket连接</li>
          <li>连接成功后，状态会显示为绿色的"connected"</li>
          <li>当系统中有任务执行、Worker状态变化等事件时，会在下方显示</li>
          <li>可以在任务详情页面执行任务来测试实时更新功能</li>
        </ul>
      </Card>
    </div>
  );
};

export default WebSocketTest;
