"""
WebSocket连接管理器
实现实时状态推送功能，支持任务执行状态、Worker分配状态、URL处理进度的实时更新
"""

import asyncio
import json
import logging
from typing import Dict, List, Set, Optional, Any
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, asdict
from fastapi import WebSocket, WebSocketDisconnect
import redis.asyncio as redis


class EventType(Enum):
    """WebSocket事件类型"""
    # 系统级事件
    WORKER_ONLINE = "worker_online"
    WORKER_OFFLINE = "worker_offline"
    SYSTEM_OVERLOAD = "system_overload"
    
    # 任务级事件
    TASK_STARTED = "task_started"
    TASK_PROGRESS = "task_progress"
    TASK_COMPLETED = "task_completed"
    TASK_FAILED = "task_failed"
    TASK_PAUSED = "task_paused"
    TASK_RESUMED = "task_resumed"
    
    # URL级事件
    URL_ASSIGNED = "url_assigned"
    URL_PROCESSING = "url_processing"
    URL_COMPLETED = "url_completed"
    URL_FAILED = "url_failed"
    
    # Worker分配事件
    WORKER_ASSIGNED = "worker_assigned"
    WORKER_LOAD_CHANGED = "worker_load_changed"
    ALLOCATION_UPDATED = "allocation_updated"


@dataclass
class WebSocketEvent:
    """WebSocket事件数据结构"""
    event_type: EventType
    data: Dict[str, Any]
    timestamp: str
    task_id: Optional[str] = None
    worker_id: Optional[str] = None
    url_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "event_type": self.event_type.value,
            "data": self.data,
            "timestamp": self.timestamp,
            "task_id": self.task_id,
            "worker_id": self.worker_id,
            "url_id": self.url_id
        }


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接 {connection_id: websocket}
        self.active_connections: Dict[str, WebSocket] = {}
        
        # 任务订阅 {task_id: set(connection_ids)}
        self.task_subscriptions: Dict[str, Set[str]] = {}
        
        # Worker订阅 {worker_id: set(connection_ids)}
        self.worker_subscriptions: Dict[str, Set[str]] = {}
        
        # 全局订阅 set(connection_ids)
        self.global_subscriptions: Set[str] = set()
        
        self.logger = logging.getLogger(__name__)
    
    async def connect(self, websocket: WebSocket, connection_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        self.logger.info(f"WebSocket connection established: {connection_id}")
    
    def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        
        # 清理订阅
        self.global_subscriptions.discard(connection_id)
        
        for task_id in list(self.task_subscriptions.keys()):
            self.task_subscriptions[task_id].discard(connection_id)
            if not self.task_subscriptions[task_id]:
                del self.task_subscriptions[task_id]
        
        for worker_id in list(self.worker_subscriptions.keys()):
            self.worker_subscriptions[worker_id].discard(connection_id)
            if not self.worker_subscriptions[worker_id]:
                del self.worker_subscriptions[worker_id]
        
        self.logger.info(f"WebSocket connection closed: {connection_id}")
    
    def subscribe_to_task(self, connection_id: str, task_id: str):
        """订阅任务事件"""
        if task_id not in self.task_subscriptions:
            self.task_subscriptions[task_id] = set()
        self.task_subscriptions[task_id].add(connection_id)
        self.logger.debug(f"Connection {connection_id} subscribed to task {task_id}")
    
    def subscribe_to_worker(self, connection_id: str, worker_id: str):
        """订阅Worker事件"""
        if worker_id not in self.worker_subscriptions:
            self.worker_subscriptions[worker_id] = set()
        self.worker_subscriptions[worker_id].add(connection_id)
        self.logger.debug(f"Connection {connection_id} subscribed to worker {worker_id}")
    
    def subscribe_global(self, connection_id: str):
        """订阅全局事件"""
        self.global_subscriptions.add(connection_id)
        self.logger.debug(f"Connection {connection_id} subscribed to global events")
    
    async def send_to_connection(self, connection_id: str, event: WebSocketEvent):
        """发送事件到指定连接"""
        if connection_id in self.active_connections:
            try:
                websocket = self.active_connections[connection_id]
                await websocket.send_text(json.dumps(event.to_dict()))
            except Exception as e:
                self.logger.error(f"Failed to send event to {connection_id}: {e}")
                self.disconnect(connection_id)
    
    async def broadcast_to_task(self, task_id: str, event: WebSocketEvent):
        """广播事件到任务订阅者"""
        if task_id in self.task_subscriptions:
            tasks = []
            for connection_id in self.task_subscriptions[task_id].copy():
                tasks.append(self.send_to_connection(connection_id, event))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
    
    async def broadcast_to_worker(self, worker_id: str, event: WebSocketEvent):
        """广播事件到Worker订阅者"""
        if worker_id in self.worker_subscriptions:
            tasks = []
            for connection_id in self.worker_subscriptions[worker_id].copy():
                tasks.append(self.send_to_connection(connection_id, event))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
    
    async def broadcast_global(self, event: WebSocketEvent):
        """广播全局事件"""
        tasks = []
        for connection_id in self.global_subscriptions.copy():
            tasks.append(self.send_to_connection(connection_id, event))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def broadcast_event(self, event: WebSocketEvent):
        """智能广播事件到相关订阅者"""
        tasks = []
        
        # 广播到全局订阅者
        tasks.append(self.broadcast_global(event))
        
        # 广播到任务订阅者
        if event.task_id:
            tasks.append(self.broadcast_to_task(event.task_id, event))
        
        # 广播到Worker订阅者
        if event.worker_id:
            tasks.append(self.broadcast_to_worker(event.worker_id, event))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)


class WebSocketManager:
    """WebSocket管理器"""
    
    def __init__(self, redis_url: str = "redis://redis:6379/0"):
        self.connection_manager = ConnectionManager()
        self.redis_client = redis.from_url(redis_url)
        self.logger = logging.getLogger(__name__)
        
        # 事件队列
        self.event_queue: asyncio.Queue = asyncio.Queue()
        self.is_running = False
        self.event_processor_task: Optional[asyncio.Task] = None
    
    async def start(self):
        """启动WebSocket管理器"""
        if not self.is_running:
            self.is_running = True
            self.event_processor_task = asyncio.create_task(self._process_events())
            self.logger.info("WebSocket manager started")
    
    async def stop(self):
        """停止WebSocket管理器"""
        if self.is_running:
            self.is_running = False
            if self.event_processor_task:
                self.event_processor_task.cancel()
                try:
                    await self.event_processor_task
                except asyncio.CancelledError:
                    pass
            await self.redis_client.close()
            self.logger.info("WebSocket manager stopped")
    
    async def handle_websocket(self, websocket: WebSocket, connection_id: str):
        """处理WebSocket连接"""
        await self.connection_manager.connect(websocket, connection_id)
        
        try:
            while True:
                # 接收客户端消息
                data = await websocket.receive_text()
                await self._handle_client_message(connection_id, data)
                
        except WebSocketDisconnect:
            self.connection_manager.disconnect(connection_id)
        except Exception as e:
            self.logger.error(f"WebSocket error for {connection_id}: {e}")
            self.connection_manager.disconnect(connection_id)
    
    async def _handle_client_message(self, connection_id: str, message: str):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            action = data.get("action")
            
            if action == "subscribe_task":
                task_id = data.get("task_id")
                if task_id:
                    self.connection_manager.subscribe_to_task(connection_id, task_id)
            
            elif action == "subscribe_worker":
                worker_id = data.get("worker_id")
                if worker_id:
                    self.connection_manager.subscribe_to_worker(connection_id, worker_id)
            
            elif action == "subscribe_global":
                self.connection_manager.subscribe_global(connection_id)
            
            elif action == "ping":
                # 心跳响应
                pong_event = WebSocketEvent(
                    event_type=EventType.SYSTEM_OVERLOAD,  # 临时使用
                    data={"type": "pong"},
                    timestamp=datetime.now().isoformat()
                )
                await self.connection_manager.send_to_connection(connection_id, pong_event)
                
        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON message from {connection_id}: {message}")
        except Exception as e:
            self.logger.error(f"Error handling message from {connection_id}: {e}")
    
    async def emit_event(self, event: WebSocketEvent):
        """发送事件"""
        await self.event_queue.put(event)
    
    async def _process_events(self):
        """处理事件队列"""
        while self.is_running:
            try:
                # 等待事件，超时1秒
                event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)
                
                # 广播事件
                await self.connection_manager.broadcast_event(event)
                
                # 可选：将事件存储到Redis以供历史查询
                await self._store_event(event)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing event: {e}")
    
    async def _store_event(self, event: WebSocketEvent):
        """存储事件到Redis"""
        try:
            event_key = f"websocket_events:{datetime.now().strftime('%Y%m%d')}"
            event_data = json.dumps(event.to_dict())
            
            # 使用列表存储事件，限制长度
            await self.redis_client.lpush(event_key, event_data)
            await self.redis_client.ltrim(event_key, 0, 999)  # 保留最近1000个事件
            await self.redis_client.expire(event_key, 86400)  # 24小时过期
            
        except Exception as e:
            self.logger.error(f"Failed to store event: {e}")
    
    # 便捷方法
    async def emit_task_started(self, task_id: str, data: Dict[str, Any]):
        """发送任务开始事件"""
        event = WebSocketEvent(
            event_type=EventType.TASK_STARTED,
            data=data,
            timestamp=datetime.now().isoformat(),
            task_id=task_id
        )
        await self.emit_event(event)
    
    async def emit_task_progress(self, task_id: str, progress_data: Dict[str, Any]):
        """发送任务进度事件"""
        event = WebSocketEvent(
            event_type=EventType.TASK_PROGRESS,
            data=progress_data,
            timestamp=datetime.now().isoformat(),
            task_id=task_id
        )
        await self.emit_event(event)
    
    async def emit_url_status_change(self, task_id: str, url_id: str, worker_id: str, status_data: Dict[str, Any]):
        """发送URL状态变化事件"""
        event_type = {
            "assigned": EventType.URL_ASSIGNED,
            "processing": EventType.URL_PROCESSING,
            "completed": EventType.URL_COMPLETED,
            "failed": EventType.URL_FAILED
        }.get(status_data.get("status"), EventType.URL_PROCESSING)
        
        event = WebSocketEvent(
            event_type=event_type,
            data=status_data,
            timestamp=datetime.now().isoformat(),
            task_id=task_id,
            worker_id=worker_id,
            url_id=url_id
        )
        await self.emit_event(event)
    
    async def emit_worker_allocation(self, task_id: str, allocation_data: Dict[str, Any]):
        """发送Worker分配事件"""
        event = WebSocketEvent(
            event_type=EventType.ALLOCATION_UPDATED,
            data=allocation_data,
            timestamp=datetime.now().isoformat(),
            task_id=task_id
        )
        await self.emit_event(event)


# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()
